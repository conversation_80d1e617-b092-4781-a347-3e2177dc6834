#!/bin/bash

echo "🔍 Shadowrocket 配置诊断工具"
echo "============================="

echo "📋 当前网络状态:"
echo "VPN DNS 服务器: $(nslookup google.com | grep "Server:" | awk '{print $2}')"
echo "本机 IP: $(curl -s --connect-timeout 5 https://api.ipify.org || echo "无法获取")"

echo -e "\n🔍 DNS 解析测试:"
echo "gate.decodo.com:"
nslookup gate.decodo.com | grep "Address:" | tail -1

echo -e "\n🧪 可能的解决方案:"

echo -e "\n方案 1: 检查 Shadowrocket 配置文件格式"
echo "确保规则格式完全正确:"
echo "DOMAIN,gate.decodo.com,DIRECT"
echo "DOMAIN-SUFFIX,decodo.com,DIRECT"
echo "IP-CIDR,*************/24,DIRECT"

echo -e "\n方案 2: 尝试使用系统 DNS"
echo "在 Shadowrocket 设置中:"
echo "1. 进入 '设置' -> 'DNS'"
echo "2. 选择 '系统' 或添加公共 DNS"
echo "3. 或者在配置文件 [General] 部分添加:"
echo "   dns-server = *******, *******"

echo -e "\n方案 3: 使用 DNS Override"
echo "在配置文件 [Host] 部分添加:"
echo "gate.decodo.com = **************"
echo "*.decodo.com = **************"

echo -e "\n方案 4: 临时绕过方案"
echo "如果规则仍不生效，我们可以:"
echo "1. 修改服务代码直接使用 IP 地址"
echo "2. 创建本地 hosts 文件映射"
echo "3. 使用其他代理服务"

echo -e "\n🔧 建议的操作顺序:"
echo "1. 先尝试方案 3 (DNS Override)"
echo "2. 如果不行，尝试方案 2 (系统 DNS)"
echo "3. 最后使用方案 4 (临时绕过)"

echo -e "\n📱 DNS Override 配置示例:"
echo "[Host]"
echo "gate.decodo.com = **************"
echo "*.decodo.com = **************"

echo -e "\n🚀 配置完成后请运行:"
echo "./verify_vpn_fix.sh"