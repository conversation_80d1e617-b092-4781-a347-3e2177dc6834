#!/bin/bash

echo "🧪 GetGoodTape 手动测试工具"
echo "=========================="

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

BASE_URL="http://localhost:8000"

# 检查服务是否运行
check_service() {
    echo -e "${BLUE}🔍 检查本地服务状态...${NC}"
    
    if curl -s "$BASE_URL/health" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ 本地服务正在运行${NC}"
        return 0
    else
        echo -e "${RED}❌ 本地服务未运行${NC}"
        echo "请先运行: ./start_local.sh"
        return 1
    fi
}

# 测试健康检查
test_health() {
    echo -e "\n${BLUE}🏥 测试健康检查...${NC}"
    
    result=$(curl -s "$BASE_URL/health")
    echo "响应: $result"
    
    if echo "$result" | grep -q '"status":"healthy"'; then
        echo -e "${GREEN}✅ 健康检查通过${NC}"
    else
        echo -e "${RED}❌ 健康检查失败${NC}"
    fi
}

# 测试代理状态
test_proxy_stats() {
    echo -e "\n${BLUE}🔄 测试代理状态...${NC}"
    
    result=$(curl -s "$BASE_URL/proxy-stats")
    echo "代理配置:"
    echo "$result" | jq '.proxy_list_sample[0:3]' 2>/dev/null || echo "$result"
    
    if echo "$result" | grep -q '"success":true'; then
        echo -e "${GREEN}✅ 代理状态获取成功${NC}"
    else
        echo -e "${RED}❌ 代理状态获取失败${NC}"
    fi
}

# 测试视频元数据提取
test_metadata() {
    echo -e "\n${BLUE}📋 测试视频元数据提取...${NC}"
    
    echo "测试 URL: https://www.youtube.com/watch?v=jNQXAC9IVRw"
    
    result=$(curl -s -X POST "$BASE_URL/extract-metadata" \
        -H "Content-Type: application/json" \
        -d '{"url": "https://www.youtube.com/watch?v=jNQXAC9IVRw"}')
    
    if echo "$result" | grep -q '"success":true'; then
        echo -e "${GREEN}✅ 元数据提取成功${NC}"
        title=$(echo "$result" | jq -r '.metadata.title' 2>/dev/null)
        duration=$(echo "$result" | jq -r '.metadata.duration' 2>/dev/null)
        echo "  标题: $title"
        echo "  时长: ${duration}秒"
    else
        echo -e "${RED}❌ 元数据提取失败${NC}"
        echo "错误: $(echo "$result" | jq -r '.error' 2>/dev/null || echo "$result")"
    fi
}

# 测试 MP3 转换
test_mp3_conversion() {
    echo -e "\n${BLUE}🎵 测试 MP3 转换...${NC}"
    
    echo "开始转换（可能需要1-2分钟）..."
    
    result=$(curl -s -X POST "$BASE_URL/convert" \
        -H "Content-Type: application/json" \
        -d '{"url": "https://www.youtube.com/watch?v=jNQXAC9IVRw", "format": "mp3", "quality": "medium"}' \
        --max-time 120)
    
    if echo "$result" | grep -q '"success":true'; then
        echo -e "${GREEN}✅ MP3 转换成功${NC}"
        filename=$(echo "$result" | jq -r '.result.filename' 2>/dev/null)
        filesize=$(echo "$result" | jq -r '.result.file_size' 2>/dev/null)
        echo "  文件名: $filename"
        echo "  文件大小: $filesize 字节"
    else
        echo -e "${RED}❌ MP3 转换失败${NC}"
        echo "错误: $(echo "$result" | jq -r '.error' 2>/dev/null || echo "$result")"
    fi
}

# 测试 MP4 转换
test_mp4_conversion() {
    echo -e "\n${BLUE}🎥 测试 MP4 转换...${NC}"
    
    echo "开始转换（可能需要2-3分钟）..."
    
    result=$(curl -s -X POST "$BASE_URL/convert" \
        -H "Content-Type: application/json" \
        -d '{"url": "https://www.youtube.com/watch?v=jNQXAC9IVRw", "format": "mp4", "quality": "low"}' \
        --max-time 180)
    
    if echo "$result" | grep -q '"success":true'; then
        echo -e "${GREEN}✅ MP4 转换成功${NC}"
        filename=$(echo "$result" | jq -r '.result.filename' 2>/dev/null)
        filesize=$(echo "$result" | jq -r '.result.file_size' 2>/dev/null)
        echo "  文件名: $filename"
        echo "  文件大小: $filesize 字节"
    else
        echo -e "${RED}❌ MP4 转换失败${NC}"
        echo "错误: $(echo "$result" | jq -r '.error' 2>/dev/null || echo "$result")"
    fi
}

# 测试 IP 代理端点
test_ip_proxy() {
    echo -e "\n${BLUE}🌐 测试 IP 代理端点...${NC}"
    
    result=$(curl -s -X POST "$BASE_URL/convert-with-ip-proxy" \
        -H "Content-Type: application/json" \
        -d '{"url": "https://www.youtube.com/watch?v=jNQXAC9IVRw", "format": "mp3", "quality": "medium"}' \
        --max-time 120)
    
    if echo "$result" | grep -q '"success":true'; then
        echo -e "${GREEN}✅ IP 代理转换成功${NC}"
        filename=$(echo "$result" | jq -r '.result.filename' 2>/dev/null)
        echo "  文件名: $filename"
    else
        echo -e "${RED}❌ IP 代理转换失败${NC}"
        echo "错误: $(echo "$result" | jq -r '.error' 2>/dev/null || echo "$result")"
    fi
}

# 显示菜单
show_menu() {
    echo -e "\n${BLUE}📋 选择要执行的测试:${NC}"
    echo "1) 健康检查"
    echo "2) 代理状态"
    echo "3) 视频元数据提取"
    echo "4) MP3 转换"
    echo "5) MP4 转换"
    echo "6) IP 代理端点"
    echo "7) 运行所有测试"
    echo "8) 退出"
    echo ""
    read -p "请选择 (1-8): " choice
}

# 运行所有测试
run_all_tests() {
    echo -e "\n${BLUE}🚀 运行所有测试...${NC}"
    
    test_health
    test_proxy_stats
    test_metadata
    test_mp3_conversion
    test_mp4_conversion
    test_ip_proxy
    
    echo -e "\n${GREEN}🎉 所有测试完成！${NC}"
}

# 主函数
main() {
    # 检查服务状态
    if ! check_service; then
        exit 1
    fi
    
    while true; do
        show_menu
        
        case $choice in
            1)
                test_health
                ;;
            2)
                test_proxy_stats
                ;;
            3)
                test_metadata
                ;;
            4)
                test_mp3_conversion
                ;;
            5)
                test_mp4_conversion
                ;;
            6)
                test_ip_proxy
                ;;
            7)
                run_all_tests
                ;;
            8)
                echo -e "${GREEN}👋 再见！${NC}"
                exit 0
                ;;
            *)
                echo -e "${RED}❌ 无效选择，请重试${NC}"
                ;;
        esac
        
        echo ""
        read -p "按回车键继续..."
    done
}

# 运行主函数
main "$@"