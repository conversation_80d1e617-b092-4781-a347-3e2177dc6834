#!/usr/bin/env python3
"""
临时测试：直接使用 IP 地址绕过 DNS 问题
"""

import requests
import json

def test_ip_direct():
    """测试直接使用 IP 地址的代理"""
    
    # 使用 IP 地址而不是域名
    test_proxies = [
        "http://spwd19mn8t:VWo_9unscw6dpAl57T@**************:10001",
        "*********************************************************",
        "*********************************************************",
    ]
    
    print("🧪 测试直接 IP 代理连接...")
    
    for i, proxy in enumerate(test_proxies, 1):
        print(f"\n🔍 测试代理 {i}: {proxy.split('@')[0]}@***")
        
        try:
            # 测试代理连接
            response = requests.get(
                "https://api.ipify.org",
                proxies={"http": proxy, "https": proxy},
                timeout=10
            )
            
            if response.status_code == 200:
                ip = response.text.strip()
                print(f"✅ 代理 {i} 连接成功，出口 IP: {ip}")
                return proxy
            else:
                print(f"❌ 代理 {i} 返回状态码: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 代理 {i} 连接失败: {e}")
    
    print("\n❌ 所有代理测试失败")
    return None

def test_service_with_ip():
    """测试服务使用 IP 代理"""
    
    print("\n🚀 测试本地服务...")
    
    # 测试健康检查
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            print("✅ 服务健康检查通过")
        else:
            print(f"❌ 服务健康检查失败: {response.status_code}")
            return
    except Exception as e:
        print(f"❌ 无法连接到本地服务: {e}")
        return
    
    # 测试元数据提取（不使用代理）
    print("\n🔍 测试直连元数据提取...")
    try:
        response = requests.post(
            "http://localhost:8000/extract-metadata",
            json={"url": "https://www.youtube.com/watch?v=jNQXAC9IVRw", "use_proxy": False},
            timeout=30
        )
        
        result = response.json()
        if result.get("success"):
            print("✅ 直连元数据提取成功")
            metadata = result.get("metadata", {})
            print(f"   标题: {metadata.get('title', 'Unknown')}")
            print(f"   时长: {metadata.get('duration', 0)} 秒")
        else:
            print(f"❌ 直连元数据提取失败: {result.get('error')}")
            
    except Exception as e:
        print(f"❌ 直连测试异常: {e}")

if __name__ == "__main__":
    print("🧪 IP 直连代理测试")
    print("=" * 30)
    
    # 测试代理连接
    working_proxy = test_ip_direct()
    
    # 测试服务
    test_service_with_ip()
    
    print("\n📋 总结:")
    if working_proxy:
        print(f"✅ 找到可用代理: {working_proxy.split('@')[0]}@***")
        print("🔧 建议: 修复 VPN DNS 分流规则以使用代理功能")
    else:
        print("❌ 所有代理都无法连接")
        print("🔧 建议: 检查 VPN 分流规则和代理配置")
    
    print("\n🛠️ 下一步:")
    print("1. 修复 Shadowrocket 分流规则")
    print("2. 确保 gate.decodo.com 解析到 149.102.253.x")
    print("3. 重新测试完整功能")