#!/bin/bash

echo "🧪 DNS Override 配置测试"
echo "======================="

echo "📋 测试不同 DNS 服务器的解析结果:"

echo -e "\n1️⃣ 当前 VPN DNS (**********):"
nslookup gate.decodo.com | grep "Address:" | tail -1

echo -e "\n2️⃣ Google DNS (*******):"
nslookup gate.decodo.com ******* | grep "Address:" | tail -3

echo -e "\n3️⃣ Cloudflare DNS (*******):"
nslookup gate.decodo.com ******* | grep "Address:" | tail -3

echo -e "\n4️⃣ 阿里 DNS (*********):"
nslookup gate.decodo.com ********* | grep "Address:" | tail -3

echo -e "\n📊 分析结果:"
VPN_IP=$(nslookup gate.decodo.com | grep "Address:" | tail -1 | awk '{print $2}')
GOOGLE_IP=$(nslookup gate.decodo.com ******* | grep "Address:" | tail -1 | awk '{print $2}')

if [[ "$VPN_IP" == "198.18.0."* ]]; then
    echo "❌ VPN DNS 仍在拦截域名解析"
    echo "✅ 建议在 Shadowrocket DNS Override 中添加: *******, *******"
else
    echo "✅ VPN DNS 解析正常"
fi

if [[ "$GOOGLE_IP" == "149."* ]]; then
    echo "✅ 公共 DNS 解析到正确的代理服务器 IP"
    echo "📋 可用的代理服务器 IP:"
    nslookup gate.decodo.com ******* | grep "Address:" | tail -3 | awk '{print "   " $2}'
else
    echo "⚠️ 公共 DNS 解析异常"
fi

echo -e "\n🛠️ 推荐配置:"
echo "在 Shadowrocket 通用设置 -> DNS Override 中添加:"
echo "*******, *******"
echo ""
echo "或者使用 DoH:"
echo "https://dns.google/dns-query"

echo -e "\n🚀 配置完成后运行测试:"
echo "./verify_vpn_fix.sh"