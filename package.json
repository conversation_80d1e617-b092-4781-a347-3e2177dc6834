{"name": "getgoodtape", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev:all": "./scripts/dev.sh", "test:health": "node scripts/test-local-api.js", "test:api": "node scripts/test-api-health.js", "test:frontend": "node scripts/test-frontend.js", "test:validation": "node scripts/test-url-validation.js", "test:all": "npm run test:health && npm run test:api && npm run test:frontend && npm run test:validation", "fix:api": "node scripts/fix-api-config.js", "perf:audit": "node scripts/performance-audit.js", "perf:optimize": "node scripts/optimize-components.js", "perf:analyze": "npm run build && npx @next/bundle-analyzer", "perf:lighthouse": "npx lighthouse http://localhost:3000 --output=html --output-path=./lighthouse-report.html", "perf:all": "npm run perf:optimize && npm run perf:audit", "build": "next build", "build:full": "npm run build:frontend && npm run build:workers", "build:frontend": "next build", "build:workers": "cd workers && npm install && npm run build", "start": "next start", "lint": "npm run lint:frontend && npm run lint:workers", "lint:frontend": "next lint", "lint:workers": "cd workers && npm run lint", "lint:fix": "npm run lint:fix:frontend && npm run lint:fix:workers", "lint:fix:frontend": "next lint --fix", "lint:fix:workers": "cd workers && npm run lint:fix", "format": "prettier --write . && cd workers && npm run format", "format:check": "prettier --check . && cd workers && npm run format:check", "type-check": "tsc --noEmit && cd workers && npm run type-check", "prepare": "husky install", "install:all": "npm install && cd workers && npm install", "postinstall": "if [ \"$VERCEL\" != \"1\" ] && [ -d workers ]; then cd workers && npm install; fi"}, "dependencies": {"@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@tanstack/react-query": "^5.83.0", "@tanstack/react-query-devtools": "^5.83.0", "@types/node": "^20.8.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.534.0", "next": "^14.2.30", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "typescript": "^5.2.2"}, "devDependencies": {"@tailwindcss/line-clamp": "^0.4.4", "autoprefixer": "^10.4.0", "critters": "^0.0.23", "eslint": "^8.0.0", "eslint-config-next": "14.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "husky": "^8.0.0", "lint-staged": "^15.0.0", "postcss": "^8.4.0", "prettier": "^3.0.0", "tailwindcss": "^3.3.0"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md,css}": ["prettier --write"]}}