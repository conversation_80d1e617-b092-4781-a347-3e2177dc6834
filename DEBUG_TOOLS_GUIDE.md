# GetGoodTape 调试工具指南

## 概述

GetGoodTape 项目现在配备了完整的调试工具套件，帮助开发者快速诊断和解决问题。调试页面位于 `/debug`，提供了多种监控和测试工具。

## 访问调试页面

```
开发环境: http://localhost:3001/debug
生产环境: https://your-domain.com/debug
```

## 调试工具组件

### 1. 🔧 主调试面板

**位置**: `/debug` 页面主体
**功能**:

- 环境信息显示
- API 测试配置
- 快速测试按钮
- 测试结果展示

**使用方法**:

1. 在测试URL输入框中输入要测试的视频URL
2. 点击各种测试按钮进行单项测试
3. 或点击"运行所有测试"进行全面测试

### 2. 📊 API 状态监控

**功能**:

- 实时监控所有API端点状态
- 显示响应时间和错误信息
- 支持自动刷新和手动重试

**监控的端点**:

- 前端健康检查 (`/api/health`)
- 平台信息 (`/api/platforms`)
- URL验证 (`/api/validate`)
- Workers API (Cloudflare)
- 视频处理服务 (Fly.io)

**状态指示**:

- 🟢 在线 (< 500ms)
- 🟡 慢速 (500ms - 2s)
- 🔴 离线 (> 2s 或错误)

### 3. 🔍 系统诊断

**功能**:

- 浏览器兼容性检查
- 网络连接测试
- API端点详细测试
- 本地存储功能验证
- 性能指标检查

**诊断项目**:

- ✅ 通过
- ⚠️ 警告
- ❌ 失败
- ⏳ 进行中

### 4. 🐛 调试控制台

**位置**: 右下角浮动按钮
**功能**:

- 拦截所有 console 输出
- 捕获未处理的错误
- 实时日志显示
- 支持日志过滤和清空

**日志级别**:

- INFO: 普通信息
- WARN: 警告信息
- ERROR: 错误信息
- DEBUG: 调试信息

### 5. 📊 性能监控

**位置**: 右上角浮动按钮
**功能**:

- 实时FPS监控
- 内存使用跟踪
- 页面加载时间
- DOM节点计数
- 网络请求统计
- 错误计数

**性能指标**:

- FPS: 帧率 (目标 > 50)
- 内存: JS堆使用 (< 50MB 良好)
- 加载时间: 页面加载耗时 (< 2s 良好)
- DOM节点: 页面元素数量 (< 1000 良好)

### 6. 🌐 网络监控

**位置**: 右下角第二个浮动按钮
**功能**:

- 拦截所有网络请求
- 显示请求详情和响应时间
- 支持 Fetch API 和 XMLHttpRequest
- 实时请求统计

**监控信息**:

- 请求方法和URL
- 响应状态码
- 响应时间
- 数据大小

## 使用场景

### 1. 开发调试

```bash
# 启动开发服务器
npm run dev

# 访问调试页面
open http://localhost:3001/debug

# 运行全面测试
点击 "运行所有测试" 按钮
```

### 2. API 连接问题诊断

1. 打开 API 状态监控
2. 点击"检查状态"
3. 查看哪些端点出现问题
4. 点击"重试"按钮重新测试单个端点

### 3. 性能问题排查

1. 打开性能监控 (📊)
2. 观察FPS和内存使用
3. 进行操作并观察指标变化
4. 查看调试控制台的错误信息

### 4. 网络请求分析

1. 打开网络监控 (🌐)
2. 点击"开始"监控
3. 执行相关操作
4. 查看请求详情和响应时间

## 常见问题诊断

### API 连接失败

**症状**: API状态显示🔴离线
**排查步骤**:

1. 检查网络连接
2. 确认API服务是否运行
3. 查看浏览器控制台错误
4. 检查CORS配置

### 前端卡住问题

**症状**: 进度条不动或界面无响应
**排查步骤**:

1. 查看调试控制台的错误信息
2. 检查性能监控的FPS
3. 观察网络监控的请求状态
4. 运行系统诊断

### 内存泄漏

**症状**: 内存使用持续增长
**排查步骤**:

1. 观察性能监控的内存指标
2. 检查DOM节点数量变化
3. 查看调试控制台的警告信息
4. 分析网络请求是否有异常

## 最佳实践

### 1. 定期监控

- 开启API状态监控的自动刷新
- 保持性能监控开启
- 定期运行系统诊断

### 2. 问题记录

- 截图保存错误状态
- 复制调试控制台的错误信息
- 记录重现步骤

### 3. 性能优化

- 监控FPS保持在50以上
- 控制内存使用在合理范围
- 优化网络请求数量和大小

## 技术实现

### 组件架构

```
/debug
├── DebugConsole.tsx      # 调试控制台
├── PerformanceMonitor.tsx # 性能监控
├── NetworkMonitor.tsx    # 网络监控
├── SystemDiagnostics.tsx # 系统诊断
└── APIStatusMonitor.tsx  # API状态监控
```

### 数据流

1. 各组件独立运行
2. 通过 console 和事件监听收集数据
3. 实时更新UI显示
4. 支持手动触发和自动刷新

## 扩展功能

### 未来计划

- [ ] 错误报告自动提交
- [ ] 性能数据导出
- [ ] 自定义监控规则
- [ ] 远程调试支持
- [ ] 移动端调试优化

### 自定义配置

可以通过修改各组件的配置来调整监控参数：

- 刷新间隔
- 阈值设置
- 监控端点
- 日志级别

## 总结

GetGoodTape 的调试工具套件提供了全面的监控和诊断能力，帮助开发者：

- 快速定位问题
- 实时监控系统状态
- 优化应用性能
- 提升开发效率

通过合理使用这些工具，可以显著提高项目的稳定性和用户体验。
