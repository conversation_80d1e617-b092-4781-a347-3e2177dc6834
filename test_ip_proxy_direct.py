#!/usr/bin/env python3
"""
直接测试 IP 代理是否能够绕过 YouTube 限制
"""

import subprocess
import sys
import os

def test_ip_proxy_youtube():
    """测试 IP 代理是否能下载 YouTube 视频"""
    
    # 测试 URL
    test_url = "https://www.youtube.com/watch?v=jNQXAC9IVRw"
    
    # IP 代理列表
    ip_proxies = [
        "*********************************************************",
        "*********************************************************",
        "*********************************************************",
    ]
    
    for i, proxy in enumerate(ip_proxies):
        print(f"\n🧪 测试代理 {i+1}: {proxy.split('@')[1]}")
        
        # 构建 yt-dlp 命令
        cmd = [
            'yt-dlp',
            '--proxy', proxy,
            '--dump-json',
            '--no-download',
            test_url
        ]
        
        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=60
            )
            
            if result.returncode == 0:
                print(f"  ✅ 代理 {i+1} 成功获取视频信息")
                # 解析标题
                import json
                try:
                    info = json.loads(result.stdout)
                    title = info.get('title', 'Unknown')
                    duration = info.get('duration', 0)
                    print(f"    标题: {title}")
                    print(f"    时长: {duration}秒")
                    return True
                except:
                    print(f"    成功但无法解析 JSON")
                    return True
            else:
                print(f"  ❌ 代理 {i+1} 失败")
                error = result.stderr[:200] if result.stderr else "Unknown error"
                print(f"    错误: {error}")
                
        except subprocess.TimeoutExpired:
            print(f"  ⏰ 代理 {i+1} 超时")
        except Exception as e:
            print(f"  💥 代理 {i+1} 异常: {e}")
    
    return False

def test_direct_connection():
    """测试直连是否被 YouTube 阻止"""
    print("\n🌐 测试直连（无代理）")
    
    test_url = "https://www.youtube.com/watch?v=jNQXAC9IVRw"
    
    cmd = [
        'yt-dlp',
        '--dump-json',
        '--no-download',
        test_url
    ]
    
    try:
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=60
        )
        
        if result.returncode == 0:
            print("  ✅ 直连成功")
            return True
        else:
            print("  ❌ 直连失败")
            error = result.stderr[:200] if result.stderr else "Unknown error"
            print(f"    错误: {error}")
            return False
            
    except Exception as e:
        print(f"  💥 直连异常: {e}")
        return False

if __name__ == "__main__":
    print("🚀 YouTube 代理测试工具")
    print("=" * 40)
    
    # 检查 yt-dlp 是否可用
    try:
        subprocess.run(['yt-dlp', '--version'], capture_output=True, check=True)
        print("✅ yt-dlp 可用")
    except:
        print("❌ yt-dlp 不可用，请先安装")
        sys.exit(1)
    
    # 测试直连
    direct_works = test_direct_connection()
    
    # 测试代理
    proxy_works = test_ip_proxy_youtube()
    
    print("\n" + "=" * 40)
    print("📊 测试结果:")
    print(f"  直连: {'✅ 可用' if direct_works else '❌ 被阻止'}")
    print(f"  代理: {'✅ 可用' if proxy_works else '❌ 不可用'}")
    
    if proxy_works:
        print("\n🎉 IP 代理可以绕过 YouTube 限制！")
        print("💡 建议：在服务中强制使用 IP 代理进行 MP4 转换")
    elif direct_works:
        print("\n🤔 直连可用，可能不需要代理")
    else:
        print("\n😞 直连和代理都不可用，需要检查网络配置")