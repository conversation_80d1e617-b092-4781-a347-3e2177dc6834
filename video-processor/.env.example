# YouTube API Configuration
YOUTUBE_API_KEY=your_youtube_api_key_here

# Residential Proxy Configuration (Recommended for YouTube)
# 推荐方案: 8GB套餐 $2.75/GB ($22/月) - 最佳性价比
# 这个代理服务商 (从截图看起来是高质量服务)
RESIDENTIAL_PROXY_USER=your_proxy_username
RESIDENTIAL_PROXY_PASS=your_proxy_password
RESIDENTIAL_PROXY_ENDPOINT=your_proxy_endpoint  # 例如: proxy.example.com:10000

# 备用代理配置
# Smartproxy (https://smartproxy.com/) - 备选方案
SMARTPROXY_USER=your_smartproxy_username
SMARTPROXY_PASS=your_smartproxy_password

# Bright Data (https://brightdata.com/) - Premium option
BRIGHTDATA_USER=your_brightdata_username
BRIGHTDATA_PASS=your_brightdata_password
BRIGHTDATA_ZONE=residential

# Datacenter Proxy (Backup option)
DATACENTER_PROXY_URL=http://user:<EMAIL>:8080

# Railway Configuration
PORT=8000
PYTHONUNBUFFERED=1

# Application Settings
MAX_FILE_SIZE_MB=500
TEMP_DIR=/tmp
LOG_LEVEL=INFO

# Proxy Settings
PROXY_TIMEOUT=30
PROXY_RETRIES=3
USE_PROXY_ROTATION=true

# YouTube Download Settings
MAX_DOWNLOAD_RETRIES=5
DOWNLOAD_TIMEOUT=300
USE_RESIDENTIAL_PROXY_FIRST=true
