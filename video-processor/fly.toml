# Fly.io configuration for video-processor service
app = "getgoodtape-video-proc"
primary_region = "nrt"  # Tokyo region for better Asia connectivity

[build]
  dockerfile = "Dockerfile"

[env]
  PORT = "8000"
  PYTHONUNBUFFERED = "1"
  # R2 Storage configuration
  R2_ENDPOINT = "http://wangdonghuiibt-cloudflare.r2.cloudflarestorage.com"
  R2_BUCKET = "getgoodtape-files"

[http_service]
  internal_port = 8000
  force_https = false
  auto_stop_machines = false
  auto_start_machines = true
  min_machines_running = 1
  processes = ["app"]

  [http_service.concurrency]
    type = "requests"
    hard_limit = 50
    soft_limit = 40

  [[http_service.checks]]
    interval = "30s"
    timeout = "10s"
    grace_period = "10s"
    method = "GET"
    path = "/health"

[[vm]]
  cpu_kind = "shared"
  cpus = 2
  memory_mb = 2048

[metrics]
  port = 9091
  path = "/metrics"

# Health check configuration is now handled by http_service above

# Auto-scaling configuration
[scaling]
  min_machines = 2
  max_machines = 5

# Restart policy
[[restart]]
  policy = "on-failure"
  max_retries = 3
