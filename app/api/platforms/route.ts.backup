import { NextRequest } from 'next/server';

const WORKERS_URL =
  process.env.NODE_ENV === 'development'
    ? 'http://localhost:8787' // 本地开发时使用本地 Workers
    : 'https://getgoodtape-api-production.wangdonghuiibt-cloudflare.workers.dev';

export async function GET(request: NextRequest) {
  try {
    const response = await fetch(`${WORKERS_URL}/api/platforms`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        Pragma: 'no-cache',
        Expires: '0',
      },
    });

    const data = await response.text();

    return new Response(data, {
      status: response.status,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        Pragma: 'no-cache',
        Expires: '0',
      },
    });
  } catch (error) {
    console.error('Proxy error:', error);
    return Response.json({ error: 'Proxy error' }, { status: 500 });
  }
}
