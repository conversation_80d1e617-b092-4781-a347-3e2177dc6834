@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
    /* 防止iOS Safari缩放 */
    -webkit-text-size-adjust: 100%;
    /* 改善移动端滚动 */
    -webkit-overflow-scrolling: touch;
  }

  body {
    /* 防止移动端橡皮筋效果 */
    overscroll-behavior: none;
    /* 改善移动端触摸反馈 */
    -webkit-tap-highlight-color: rgba(255, 140, 66, 0.2);
    /* 平滑的主题切换过渡 */
    transition:
      background-color 0.3s ease,
      color 0.3s ease;
  }

  /* 暗色模式下的触摸反馈 */
  .dark body {
    -webkit-tap-highlight-color: rgba(251, 146, 60, 0.2);
  }

  /* 移动端输入框优化 */
  input,
  textarea,
  select {
    /* 防止iOS Safari缩放 */
    font-size: 16px;
    /* 改善移动端输入体验 */
    -webkit-appearance: none;
    border-radius: 0;
  }

  /* 移动端按钮优化 */
  button {
    /* 改善触摸目标大小 */
    min-height: 44px;
    /* 防止双击缩放 */
    touch-action: manipulation;
  }

  /* Line clamp utilities */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }
  .dark {
    --background: 0 0% 8%;
    --foreground: 0 0% 95%;
    --card: 0 0% 12%;
    --card-foreground: 0 0% 95%;
    --popover: 0 0% 12%;
    --popover-foreground: 0 0% 95%;
    --primary: 221 83% 53%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 20%;
    --secondary-foreground: 0 0% 95%;
    --muted: 0 0% 20%;
    --muted-foreground: 0 0% 75%;
    --accent: 0 0% 20%;
    --accent-foreground: 0 0% 95%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 25%;
    --input: 0 0% 20%;
    --ring: 221 83% 53%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer components {
  .btn-primary {
    @apply bg-brand-primary hover:bg-brand-primary-hover text-white font-medium px-6 py-3 rounded-lg transition-colors duration-200 shadow-md;
    @apply dark:bg-brand-primary dark:hover:bg-brand-primary-hover;
    /* 移动端触摸优化 */
    min-height: 44px;
    touch-action: manipulation;
  }

  .btn-secondary {
    @apply bg-neutral-panel hover:bg-neutral-border text-neutral-text font-medium px-6 py-3 rounded-lg transition-colors duration-200 border border-neutral-border;
    @apply dark:bg-dark-panel dark:hover:bg-dark-panel-hover dark:text-dark-text dark:border-dark-border;
    /* 移动端触摸优化 */
    min-height: 44px;
    touch-action: manipulation;
  }

  .brand-gradient {
    background: linear-gradient(135deg, #4f46e5 0%, #6366f1 100%);
  }

  .dark .brand-gradient {
    background: linear-gradient(135deg, #fb923c 0%, #fcd34d 100%);
  }

  /* 移动端专用样式 */
  .mobile-container {
    @apply px-4 sm:px-6 lg:px-8;
  }

  .mobile-card {
    @apply bg-white/80 backdrop-blur-sm rounded-2xl p-4 sm:p-6 lg:p-8 border border-neutral-border shadow-sm;
    @apply dark:bg-dark-panel/90 dark:border-dark-border dark:backdrop-blur-sm;
    transition:
      background-color 0.3s ease,
      border-color 0.3s ease;
    /* 暗色模式下增强对比度 */
  }

  .dark .mobile-card {
    box-shadow:
      0 4px 6px -1px rgba(0, 0, 0, 0.3),
      0 2px 4px -1px rgba(0, 0, 0, 0.2);
  }

  .mobile-input {
    @apply w-full px-4 py-3 rounded-lg border border-neutral-border focus:outline-none focus:ring-2 focus:ring-brand-primary focus:border-transparent bg-white;
    @apply dark:bg-dark-panel dark:border-dark-border dark:text-dark-text dark:focus:ring-brand-primary;
    @apply dark:placeholder-dark-text-muted;
    /* 移动端输入优化 */
    font-size: 16px;
    -webkit-appearance: none;
    transition:
      background-color 0.3s ease,
      border-color 0.3s ease,
      color 0.3s ease;
  }

  /* 确保 radio 和 checkbox 不受 mobile-input 影响 */
  input[type='radio'],
  input[type='checkbox'] {
    @apply w-auto px-0 py-0 rounded-none border-0;
    -webkit-appearance: auto;
    font-size: inherit;
  }

  .mobile-grid {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4;
  }

  .platform-card {
    background-color: #f0f0f0 !important;
    border: 1px solid #eaeaea !important;
    color: #1e1e1e !important;
  }

  .dark .platform-card {
    background-color: #2d2d2d !important;
    border: 1px solid #404040 !important;
    color: #f9f9f9 !important;
  }

  .mobile-flex {
    @apply flex flex-col sm:flex-row gap-3;
  }

  /* 移动端触摸友好的链接 */
  .mobile-link {
    @apply inline-flex min-h-[44px] items-center justify-center;
    touch-action: manipulation;
    transition: all 0.2s ease;
  }

  /* 移动端滚动优化 */
  .mobile-scroll {
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
  }

  /* 移动端手势优化 */
  .mobile-swipe {
    touch-action: pan-x pan-y;
  }

  .mobile-no-select {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  /* 移动端焦点优化 */
  .mobile-focus:focus {
    outline: 2px solid #ff8c42;
    outline-offset: 2px;
  }

  /* 移动端动画优化 */
  @media (prefers-reduced-motion: reduce) {
    .mobile-animate {
      animation: none !important;
      transition: none !important;
    }
  }

  /* 移动端键盘适配 */
  .mobile-keyboard-adjust {
    transition: margin-bottom 0.3s ease;
  }

  /* 移动端输入框优化 */
  @media (max-width: 640px) {
    input[type='url'],
    input[type='text'],
    textarea,
    select {
      font-size: 16px !important; /* 防止 iOS Safari 缩放 */
    }

    /* 移动端按钮最小触摸目标 */
    button,
    .mobile-link,
    label[role='button'] {
      min-height: 44px;
      min-width: 44px;
    }

    /* 移动端卡片间距优化 */
    .card {
      margin-bottom: 1rem;
    }

    /* 移动端文字大小优化 */
    .text-xs {
      font-size: 0.75rem;
    }

    .text-sm {
      font-size: 0.875rem;
    }
  }

  /* 移动端安全区域适配 */
  .mobile-safe-area {
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
    padding-bottom: env(safe-area-inset-bottom);
  }

  /* 性能优化相关样式 */
  .reduce-animations * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .low-end-device {
    /* 低端设备优化 */
    will-change: auto !important;
  }

  .low-end-device * {
    /* 禁用复杂的CSS效果 */
    box-shadow: none !important;
    text-shadow: none !important;
    filter: none !important;
    backdrop-filter: none !important;
  }

  .save-data img,
  .save-data video {
    /* 省流量模式下的图片优化 */
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }

  /* 移动端滚动性能优化 */
  .mobile-scroll-optimized {
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
    will-change: scroll-position;
  }

  /* 移动端图片懒加载优化 */
  .mobile-lazy-image {
    content-visibility: auto;
    contain-intrinsic-size: 200px;
  }

  /* 跨浏览器兼容性修复 */

  /* Safari 特定修复 */
  @supports (-webkit-appearance: none) {
    .mobile-input {
      /* 修复 Safari 输入框样式 */
      -webkit-appearance: none;
      border-radius: 0;
    }

    .mobile-button {
      /* 修复 Safari 按钮样式 */
      -webkit-appearance: none;
    }
  }

  /* Firefox 特定修复 */
  @-moz-document url-prefix() {
    .mobile-input {
      /* Firefox 输入框修复 */
      -moz-appearance: none;
    }
  }

  /* 老版本 iOS Safari 修复 */
  @supports (-webkit-touch-callout: none) {
    .mobile-container {
      /* 修复老版本 iOS 的滚动问题 */
      -webkit-overflow-scrolling: touch;
    }
  }

  /* Android Chrome 特定修复 */
  @media screen and (-webkit-min-device-pixel-ratio: 0) {
    .mobile-input:focus {
      /* 防止 Android Chrome 缩放 */
      font-size: 16px;
    }
  }

  /* 高对比度模式支持 */
  @media (prefers-contrast: high) {
    .mobile-card {
      border: 2px solid;
    }

    .mobile-button {
      border: 2px solid;
    }
  }

  /* 系统偏好深色模式支持（当用户选择跟随系统时） */
  @media (prefers-color-scheme: dark) {
    html:not(.light) .mobile-card {
      background-color: rgba(30, 41, 59, 0.8);
      color: #f1f5f9;
      border-color: #475569;
    }

    html:not(.light) .mobile-input {
      background-color: #1e293b;
      color: #f1f5f9;
      border-color: #475569;
    }

    html:not(.light) body {
      background-color: #0f172a;
      color: #f1f5f9;
    }
  }

  /* 降低动画偏好支持 */
  @media (prefers-reduced-motion: reduce) {
    .mobile-animate,
    .mobile-button,
    .mobile-link {
      animation: none !important;
      transition: none !important;
    }
  }

  /* 暗色模式下的额外样式优化 */
  .dark {
    /* 改善文本可读性 */
    --tw-prose-body: #cbd5e1;
    --tw-prose-headings: #f1f5f9;
    --tw-prose-lead: #94a3b8;
    --tw-prose-links: #fb923c;
    --tw-prose-bold: #f1f5f9;
    --tw-prose-counters: #94a3b8;
    --tw-prose-bullets: #475569;
  }

  /* 暗色模式下的选择文本样式 */
  .dark ::selection {
    background-color: rgba(251, 146, 60, 0.3);
    color: #f1f5f9;
  }

  /* 暗色模式下的滚动条样式 */
  .dark ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  .dark ::-webkit-scrollbar-track {
    background: #1e293b;
  }

  .dark ::-webkit-scrollbar-thumb {
    background: #475569;
    border-radius: 4px;
  }

  .dark ::-webkit-scrollbar-thumb:hover {
    background: #64748b;
  }

  /* 暗色模式下的焦点样式 */
  .dark *:focus {
    outline-color: #fb923c;
  }

  /* Brand color fixes for consistent theming */
  .text-deep-brown {
    color: #8b4513 !important;
  }

  .dark .text-deep-brown {
    color: #f9f9f9 !important;
  }

  .bg-warm-orange {
    background-color: #ff8c42 !important;
  }

  .dark .bg-warm-orange {
    background-color: #e67a35 !important;
  }

  .bg-mint-green {
    background-color: #98fb98 !important;
  }

  .dark .bg-mint-green {
    background-color: #7fe87f !important;
  }

  .bg-cream {
    background-color: #fdf6e3 !important;
  }

  .dark .bg-cream {
    background-color: #2d2d2d !important;
  }

  .bg-tape-gold {
    background-color: #daa520 !important;
  }

  .dark .bg-tape-gold {
    background-color: #b8941c !important;
  }

  /* Ensure button text is always visible */
  .bg-warm-orange {
    color: white !important;
  }

  .bg-mint-green {
    color: #8b4513 !important;
  }

  .dark .bg-mint-green {
    color: #1e1e1e !important;
  }

  /* 修复边框颜色 */
  .border-gray-300 {
    border-color: #d1d5db !important;
  }

  .dark .border-gray-300 {
    border-color: #404040 !important;
  }

  /* 修复悬停背景色 */
  .hover\:bg-gray-50:hover {
    background-color: #f9fafb !important;
  }

  .dark .hover\:bg-gray-50:hover {
    background-color: #3a3a3a !important;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
