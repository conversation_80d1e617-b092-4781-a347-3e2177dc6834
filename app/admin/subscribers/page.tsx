'use client';

import { useState } from 'react';

interface Subscriber {
  email: string;
  timestamp: string;
  ip?: string;
  userAgent?: string;
}

export default function SubscribersPage() {
  const [subscribers, setSubscribers] = useState<Subscriber[]>([]);
  const [loading, setLoading] = useState(true);
  const [password, setPassword] = useState('');
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  const handleAuth = (e: React.FormEvent) => {
    e.preventDefault();
    // Simple password protection (in production, use proper authentication)
    if (password === 'getgoodtape2024') {
      setIsAuthenticated(true);
      fetchSubscribers();
    } else {
      alert('密码错误');
    }
  };

  const fetchSubscribers = async () => {
    try {
      const response = await fetch('/api/subscribers');
      if (response.ok) {
        const data = (await response.json()) as { subscribers: Subscriber[] };
        setSubscribers(data.subscribers || []);
      }
    } catch (error) {
      console.error('Failed to fetch subscribers:', error);
    } finally {
      setLoading(false);
    }
  };

  const exportCSV = () => {
    const csvContent = [
      'Email,Subscription Date,IP Address,User Agent',
      ...subscribers.map(
        sub =>
          `"${sub.email}","${new Date(sub.timestamp).toLocaleString()}","${sub.ip || ''}","${sub.userAgent || ''}"`
      ),
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `getgoodtape-subscribers-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-cream flex items-center justify-center">
        <div className="bg-white p-8 rounded-lg shadow-lg max-w-md w-full">
          <h1 className="text-2xl font-bold text-deep-brown mb-6 text-center">
            管理员登录
          </h1>
          <form onSubmit={handleAuth}>
            <input
              type="password"
              value={password}
              onChange={e => setPassword(e.target.value)}
              placeholder="请输入管理员密码"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg mb-4 focus:outline-none focus:ring-2 focus:ring-mint-green"
              required
            />
            <button
              type="submit"
              className="w-full bg-mint-green text-deep-brown font-medium py-3 rounded-lg hover:bg-green-400 transition-colors"
            >
              登录
            </button>
          </form>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-cream flex items-center justify-center">
        <div className="text-deep-brown text-xl">加载中...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-cream">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          <div className="flex justify-between items-center mb-8">
            <h1 className="text-3xl font-bold text-deep-brown">
              GetGoodTape 订阅者管理
            </h1>
            <div className="flex gap-4">
              <button
                onClick={exportCSV}
                className="bg-warm-orange text-white px-6 py-2 rounded-lg hover:bg-orange-500 transition-colors"
              >
                导出 CSV
              </button>
              <button
                onClick={() => (window.location.href = '/')}
                className="bg-deep-brown text-white px-6 py-2 rounded-lg hover:bg-amber-800 transition-colors"
              >
                返回首页
              </button>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-lg overflow-hidden">
            <div className="px-6 py-4 bg-warm-orange text-white">
              <h2 className="text-xl font-semibold">
                总订阅者数量: {subscribers.length}
              </h2>
            </div>

            {subscribers.length === 0 ? (
              <div className="p-8 text-center text-deep-brown/70">
                暂无订阅者
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        邮箱地址
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        订阅时间
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        IP地址
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        浏览器
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {subscribers.map((subscriber, index) => (
                      <tr key={index} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-deep-brown">
                          {subscriber.email}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {new Date(subscriber.timestamp).toLocaleString(
                            'zh-CN'
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {subscriber.ip || 'N/A'}
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-500 max-w-xs truncate">
                          {subscriber.userAgent || 'N/A'}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
