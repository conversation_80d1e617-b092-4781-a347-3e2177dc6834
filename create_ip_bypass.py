#!/usr/bin/env python3
"""
创建 IP 绕过版本的代理配置
"""

import os
import shutil

def create_ip_bypass():
    """创建使用 IP 地址的代理配置"""
    
    print("🔧 创建 IP 绕过版本...")
    
    # 备份原始文件
    original_file = "video-processor/proxy_config.py"
    backup_file = "video-processor/proxy_config.py.backup"
    
    if not os.path.exists(backup_file):
        shutil.copy2(original_file, backup_file)
        print(f"✅ 已备份原始文件到: {backup_file}")
    
    # 读取原始文件
    with open(original_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 替换域名为 IP 地址
    ip_content = content.replace(
        'gate.decodo.com',
        '**************'
    )
    
    # 写入修改后的文件
    with open(original_file, 'w', encoding='utf-8') as f:
        f.write(ip_content)
    
    print("✅ 已创建 IP 绕过版本")
    print("📋 修改内容:")
    print("  gate.decodo.com → **************")
    
    return True

def restore_original():
    """恢复原始配置"""
    
    original_file = "video-processor/proxy_config.py"
    backup_file = "video-processor/proxy_config.py.backup"
    
    if os.path.exists(backup_file):
        shutil.copy2(backup_file, original_file)
        print("✅ 已恢复原始配置")
        return True
    else:
        print("❌ 备份文件不存在")
        return False

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "restore":
        restore_original()
    else:
        create_ip_bypass()
        
        print("\n🧪 现在可以测试:")
        print("1. 重启本地服务: pkill -f main.py && cd video-processor && python3 main.py &")
        print("2. 运行测试: ./verify_vpn_fix.sh")
        print("\n🔄 恢复原始配置: python3 create_ip_bypass.py restore")