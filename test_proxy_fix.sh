#!/bin/bash

echo "🧪 VPN + 代理修复验证测试"
echo "=========================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 测试函数
test_endpoint() {
    local url=$1
    local description=$2
    local timeout=${3:-30}
    
    echo -n "  测试 $description... "
    
    if curl -s --max-time $timeout "$url" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ 成功${NC}"
        return 0
    else
        echo -e "${RED}❌ 失败${NC}"
        return 1
    fi
}

# 1. 基础连接测试
echo ""
echo "🌐 基础连接测试"
test_endpoint "https://getgoodtape-video-proc.fly.dev/health" "服务健康检查"

# 2. 代理状态测试
echo ""
echo "🔄 代理状态测试"
echo "  获取代理统计信息..."

proxy_stats=$(curl -s --max-time 30 "https://getgoodtape-video-proc.fly.dev/proxy-stats" 2>/dev/null)

if [ $? -eq 0 ] && [ -n "$proxy_stats" ]; then
    echo -e "  ${GREEN}✅ 代理状态获取成功${NC}"
    
    # 解析成功率
    success_count=$(echo "$proxy_stats" | grep -o '"success":[0-9]*' | grep -o '[0-9]*' | awk '{sum+=$1} END {print sum+0}')
    failure_count=$(echo "$proxy_stats" | grep -o '"failure":[0-9]*' | grep -o '[0-9]*' | awk '{sum+=$1} END {print sum+0}')
    total_attempts=$((success_count + failure_count))
    
    if [ $total_attempts -gt 0 ]; then
        success_rate=$(echo "scale=2; $success_count * 100 / $total_attempts" | bc 2>/dev/null || echo "0")
        echo "  📊 代理统计:"
        echo "    - 成功次数: $success_count"
        echo "    - 失败次数: $failure_count"
        echo "    - 总尝试次数: $total_attempts"
        echo "    - 成功率: ${success_rate}%"
        
        if [ "$success_count" -gt 0 ]; then
            echo -e "  ${GREEN}🎉 代理工作正常！${NC}"
        else
            echo -e "  ${RED}⚠️ 所有代理失败 - VPN 冲突未解决${NC}"
        fi
    else
        echo "  📊 暂无代理使用统计"
    fi
else
    echo -e "  ${RED}❌ 无法获取代理状态${NC}"
fi

# 3. 功能测试
echo ""
echo "🎵 功能测试"
echo "  测试 MP3 转换功能..."

convert_result=$(curl -s --max-time 120 -X POST "https://getgoodtape-video-proc.fly.dev/convert" \
  -H "Content-Type: application/json" \
  -d '{
    "url": "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
    "format": "mp3",
    "quality": "medium"
  }' 2>/dev/null)

if [ $? -eq 0 ] && echo "$convert_result" | grep -q '"success":true'; then
    echo -e "  ${GREEN}✅ MP3 转换成功${NC}"
    
    # 提取文件信息
    filename=$(echo "$convert_result" | grep -o '"filename":"[^"]*"' | cut -d'"' -f4)
    file_size=$(echo "$convert_result" | grep -o '"file_size":[0-9]*' | cut -d':' -f2)
    
    if [ -n "$filename" ] && [ -n "$file_size" ]; then
        file_size_mb=$(echo "scale=2; $file_size / 1024 / 1024" | bc 2>/dev/null || echo "unknown")
        echo "    - 文件名: $filename"
        echo "    - 文件大小: ${file_size_mb} MB"
    fi
else
    echo -e "  ${RED}❌ MP3 转换失败${NC}"
    if [ -n "$convert_result" ]; then
        error_msg=$(echo "$convert_result" | grep -o '"error":"[^"]*"' | cut -d'"' -f4)
        if [ -n "$error_msg" ]; then
            echo "    错误信息: $error_msg"
        fi
    fi
fi

# 4. 总结和建议
echo ""
echo "📋 测试总结"
echo "============"

if [ "$success_count" -gt 0 ]; then
    echo -e "${GREEN}🎉 恭喜！VPN + 代理冲突已解决${NC}"
    echo "✅ 代理工作正常"
    echo "✅ 转换功能正常"
    echo ""
    echo "💡 建议："
    echo "  - 保持当前 VPN 分流配置"
    echo "  - 定期检查代理状态"
    echo "  - 监控转换成功率"
else
    echo -e "${YELLOW}⚠️ VPN + 代理冲突仍未完全解决${NC}"
    echo ""
    echo "🔧 建议的解决步骤："
    echo "  1. 检查 VPN 分流规则格式是否正确"
    echo "  2. 重启 VPN 客户端使规则生效"
    echo "  3. 确认以下域名和 IP 在直连列表中："
    echo "     - gate.decodo.com"
    echo "     - *.decodo.com"
    echo "     - ***********/20"
    echo "  4. 如果仍有问题，考虑临时关闭 VPN 测试"
    echo ""
    echo "📞 需要帮助？运行本地诊断："
    echo "     cd video-processor && ./diagnose_vpn.sh"
fi

echo ""
echo "🔗 有用的链接："
echo "  - 代理状态: https://getgoodtape-video-proc.fly.dev/proxy-stats"
echo "  - 服务健康: https://getgoodtape-video-proc.fly.dev/health"
echo "  - 完整解决方案: 查看 VPN_PROXY_SOLUTION.md"

echo ""
echo "测试完成！"