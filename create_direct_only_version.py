#!/usr/bin/env python3
"""
创建完全直连版本（跳过所有代理逻辑）
"""

import os
import shutil

def create_direct_only_version():
    """创建完全直连版本"""
    
    print("🔧 创建完全直连版本...")
    
    # 备份原始文件
    main_file = "video-processor/main.py"
    backup_file = "video-processor/main.py.direct_backup"
    
    if not os.path.exists(backup_file):
        shutil.copy2(main_file, backup_file)
        print(f"✅ 已备份原始文件到: {backup_file}")
    
    # 读取原始文件
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 修改 extract_video_metadata 函数，跳过 subprocess 方法
    modified_content = content.replace(
        '''        # Method 1: Try subprocess yt-dlp first (works with proxy)
        logger.info("🚀 Trying subprocess yt-dlp method...")
        subprocess_result = extract_metadata_with_subprocess(url, use_proxy=True)

        if subprocess_result.get('success'):
            logger.info("✅ Subprocess method successful!")
            return subprocess_result['metadata']
        else:
            logger.warning(f"⚠️ Subprocess method failed: {subprocess_result.get('error')}")

        # Method 2: Fallback to Python library yt-dlp''',
        '''        # 🔧 DIRECT CONNECTION ONLY - Skip subprocess method
        logger.info("🚀 Using direct Python library yt-dlp (no proxy)...")'''
    )
    
    # 修改 yt-dlp 配置，确保不使用代理
    modified_content = modified_content.replace(
        '''        # Configure yt-dlp options with latest 2025 best practices
        ydl_opts = {''',
        '''        # Configure yt-dlp options for DIRECT CONNECTION ONLY
        ydl_opts = {
            'proxy': None,  # 强制不使用代理'''
    )
    
    # 移除代理相关的配置
    modified_content = modified_content.replace(
        '''        # Try multiple extraction methods with environment-specific optimizations
        import os
        is_cloud_env = bool(os.getenv('RENDER') or os.getenv('RAILWAY') or os.getenv('HEROKU'))''',
        '''        # DIRECT CONNECTION - No proxy methods
        logger.info("🔄 Using direct connection (no proxy)")
        
        # Single extraction attempt with direct connection
        try:
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                info = ydl.extract_info(url, download=False)
                logger.info("✅ Direct extraction successful!")
                return info
        except Exception as e:
            logger.error(f"❌ Direct extraction failed: {e}")
            raise Exception(f"Could not extract YouTube video information. Last error: {e}")'''
    )
    
    # 写入修改后的文件
    with open(main_file, 'w', encoding='utf-8') as f:
        f.write(modified_content)
    
    print("✅ 已创建完全直连版本")
    print("📋 修改内容:")
    print("  - 跳过 subprocess 代理方法")
    print("  - 强制设置 proxy: None")
    print("  - 使用单一直连提取方法")
    
    return True

def restore_original():
    """恢复原始版本"""
    
    main_file = "video-processor/main.py"
    backup_file = "video-processor/main.py.direct_backup"
    
    if os.path.exists(backup_file):
        shutil.copy2(backup_file, main_file)
        print("✅ 已恢复原始版本")
        return True
    else:
        print("❌ 备份文件不存在")
        return False

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "restore":
        restore_original()
    else:
        create_direct_only_version()
        
        print("\n🧪 现在可以测试:")
        print("1. 重启本地服务: pkill -f main.py && cd video-processor && python3 main.py &")
        print("2. 测试元数据提取: curl -X POST 'http://localhost:8000/extract-metadata' -H 'Content-Type: application/json' -d '{\"url\": \"https://www.youtube.com/watch?v=jNQXAC9IVRw\"}'")
        print("\n🔄 恢复原始版本: python3 create_direct_only_version.py restore")