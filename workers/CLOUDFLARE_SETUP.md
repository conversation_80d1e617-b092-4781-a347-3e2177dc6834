# Cloudflare Infrastructure Setup Guide

This guide walks you through setting up the complete Cloudflare infrastructure for GetGoodTape, including D1 database, R2 storage, and KV namespaces.

## Prerequisites

1. **Cloudflare Account**: Sign up at [cloudflare.com](https://cloudflare.com)
2. **Wrangler CLI**: Install globally with `npm install -g wrangler`
3. **Authentication**: Run `wrangler login` to authenticate with your Cloudflare account

## Quick Setup (Recommended)

For development environment:

```bash
cd workers
npm run setup:all
```

For production environment:

```bash
cd workers
npm run setup:all:prod
```

## Manual Setup

### 1. D1 Database Setup

#### Create Databases

```bash
# Development
wrangler d1 create getgoodtape-dev

# Production
wrangler d1 create getgoodtape-prod
```

#### Run Migrations

```bash
# Development
npm run setup:db:dev

# Production
npm run setup:db:prod
```

#### Verify Database

```bash
# List tables
wrangler d1 execute getgoodtape-dev --command="SELECT name FROM sqlite_master WHERE type='table';" --env development

# Check platforms data
wrangler d1 execute getgoodtape-dev --command="SELECT * FROM platforms;" --env development
```

### 2. R2 Storage Setup

#### Create Buckets

```bash
# Development
npm run setup:r2:dev

# Production
npm run setup:r2:prod
```

#### Manual Bucket Creation

```bash
# Development
wrangler r2 bucket create getgoodtape-files-dev

# Production
wrangler r2 bucket create getgoodtape-files
```

### 3. KV Namespace Setup

#### Create Namespaces

```bash
# Development
npm run setup:kv:dev

# Production
npm run setup:kv:prod
```

#### Manual Namespace Creation

```bash
# Development
wrangler kv:namespace create getgoodtape-cache-dev

# Production
wrangler kv:namespace create getgoodtape-cache
```

## Configuration

### Update wrangler.toml

After running the setup scripts, you'll need to update the `wrangler.toml` file with the actual IDs generated by Cloudflare:

```toml
name = "getgoodtape-api"
main = "src/index.ts"
compatibility_date = "2024-01-01"
node_compat = true

[env.development]
vars = { ENVIRONMENT = "development" }

[[env.development.d1_databases]]
binding = "DB"
database_name = "getgoodtape-dev"
database_id = "your-actual-dev-database-id"

[[env.development.r2_buckets]]
binding = "STORAGE"
bucket_name = "getgoodtape-files-dev"

[[env.development.kv_namespaces]]
binding = "CACHE"
id = "your-actual-dev-kv-namespace-id"

[env.production]
vars = { ENVIRONMENT = "production" }

[[env.production.d1_databases]]
binding = "DB"
database_name = "getgoodtape-prod"
database_id = "your-actual-prod-database-id"

[[env.production.r2_buckets]]
binding = "STORAGE"
bucket_name = "getgoodtape-files"

[[env.production.kv_namespaces]]
binding = "CACHE"
id = "your-actual-prod-kv-namespace-id"
```

### Environment Variables

Create a `.dev.vars` file for local development:

```bash
# .dev.vars (for local development only)
ENVIRONMENT=development
PROCESSING_SERVICE_URL=http://localhost:8000
```

## Database Schema

### Tables Created

1. **conversion_jobs**: Stores video conversion job information
2. **platforms**: Configuration for supported video platforms
3. **usage_stats**: Analytics and usage statistics

### Default Platforms

The setup automatically creates entries for:

- YouTube (youtube.com)
- TikTok (tiktok.com)
- X/Twitter (x.com, twitter.com)
- Facebook (facebook.com)
- Instagram (instagram.com)

## Storage Structure

### R2 Bucket Organization

```
getgoodtape-files/
├── temp/           # Temporary files during conversion
├── converted/      # Final converted files
├── thumbnails/     # Video thumbnails
└── metadata/       # Cached metadata files
```

### KV Namespace Usage

- **Video Metadata**: `metadata:{videoId}` (TTL: 1 hour)
- **Conversion Status**: `status:{jobId}` (TTL: 30 minutes)
- **Platform Info**: `platforms` (TTL: 24 hours)
- **Rate Limiting**: `rate:{ip}` (TTL: varies)

## Testing the Setup

### Test Database Connection

```bash
# Test query
wrangler d1 execute getgoodtape-dev --command="SELECT COUNT(*) as platform_count FROM platforms;" --env development
```

### Test R2 Storage

```bash
# List buckets
wrangler r2 bucket list

# Test upload (optional)
echo "test" > test.txt
wrangler r2 object put getgoodtape-files-dev/test.txt --file test.txt
wrangler r2 object get getgoodtape-files-dev/test.txt
rm test.txt
```

### Test KV Namespace

```bash
# List namespaces
wrangler kv:namespace list

# Test put/get (optional)
wrangler kv:key put --binding CACHE "test-key" "test-value" --env development
wrangler kv:key get --binding CACHE "test-key" --env development
```

## Troubleshooting

### Common Issues

1. **Authentication Error**

   ```bash
   wrangler login
   ```

2. **Database ID Not Found**
   - Check the output of `wrangler d1 create` command
   - Update `wrangler.toml` with the correct database ID

3. **R2 Bucket Already Exists**
   - This is normal if you've run the setup before
   - The script will continue with existing buckets

4. **KV Namespace Creation Failed**
   - Check your Cloudflare account limits
   - Ensure you have the necessary permissions

### Getting Help

1. Check Cloudflare Workers documentation: https://developers.cloudflare.com/workers/
2. Check Wrangler CLI documentation: https://developers.cloudflare.com/workers/wrangler/
3. View Cloudflare dashboard: https://dash.cloudflare.com/

## Next Steps

After completing the infrastructure setup:

1. **Update Configuration**: Ensure all IDs in `wrangler.toml` are correct
2. **Test Deployment**: Run `wrangler dev` to test locally
3. **Deploy**: Run `wrangler deploy --env development` for development deployment
4. **Monitor**: Check Cloudflare dashboard for usage and performance metrics

## Security Notes

- Never commit `.dev.vars` or production secrets to version control
- Use Cloudflare's built-in security features (rate limiting, DDoS protection)
- Regularly rotate API keys and tokens
- Monitor usage to detect unusual activity
