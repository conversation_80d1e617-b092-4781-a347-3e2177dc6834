{"root": true, "extends": ["eslint:recommended"], "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint"], "parserOptions": {"ecmaVersion": "latest", "sourceType": "module"}, "env": {"browser": true, "es6": true, "worker": true}, "rules": {"@typescript-eslint/no-unused-vars": "error", "@typescript-eslint/no-explicit-any": "warn", "prefer-const": "error", "no-var": "error", "no-undef": "off"}, "ignorePatterns": ["dist/", "node_modules/"]}