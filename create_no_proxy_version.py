#!/usr/bin/env python3
"""
创建完全不使用代理的本地测试版本
"""

import os
import shutil

def create_no_proxy_version():
    """创建不使用代理的版本"""
    
    print("🔧 创建无代理版本...")
    
    # 备份原始文件
    main_file = "video-processor/main.py"
    backup_file = "video-processor/main.py.no_proxy_backup"
    
    if not os.path.exists(backup_file):
        shutil.copy2(main_file, backup_file)
        print(f"✅ 已备份原始文件到: {backup_file}")
    
    # 读取原始文件
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 修改内容，强制禁用代理
    modified_content = content.replace(
        'use_proxy = request.use_proxy if hasattr(request, "use_proxy") else True',
        'use_proxy = False  # 强制禁用代理用于本地测试'
    )
    
    # 如果上面的替换没找到，尝试其他可能的位置
    if modified_content == content:
        # 在 extract_metadata 函数中添加禁用代理的逻辑
        modified_content = content.replace(
            'async def extract_metadata(request: VideoRequest):',
            '''async def extract_metadata(request: VideoRequest):
    # 强制禁用代理用于本地测试
    request.use_proxy = False'''
        )
    
    # 写入修改后的文件
    with open(main_file, 'w', encoding='utf-8') as f:
        f.write(modified_content)
    
    print("✅ 已创建无代理版本")
    print("📋 修改内容:")
    print("  强制设置 use_proxy = False")
    
    return True

def restore_original():
    """恢复原始版本"""
    
    main_file = "video-processor/main.py"
    backup_file = "video-processor/main.py.no_proxy_backup"
    
    if os.path.exists(backup_file):
        shutil.copy2(backup_file, main_file)
        print("✅ 已恢复原始版本")
        return True
    else:
        print("❌ 备份文件不存在")
        return False

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "restore":
        restore_original()
    else:
        create_no_proxy_version()
        
        print("\n🧪 现在可以测试:")
        print("1. 重启本地服务: pkill -f main.py && cd video-processor && python3 main.py &")
        print("2. 测试元数据提取: curl -X POST 'http://localhost:8000/extract-metadata' -H 'Content-Type: application/json' -d '{\"url\": \"https://www.youtube.com/watch?v=jNQXAC9IVRw\"}'")
        print("\n🔄 恢复原始版本: python3 create_no_proxy_version.py restore")