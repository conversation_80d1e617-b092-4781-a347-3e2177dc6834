#!/bin/bash

echo "🧪 测试新 IP 段配置后的效果"
echo "=========================="

echo "1. 测试 DNS 解析..."
decodo_ip=$(nslookup gate.decodo.com | grep "Address:" | tail -1 | awk '{print $2}')
echo "   gate.decodo.com -> $decodo_ip"

if [[ "$decodo_ip" == 149.102.253.* ]]; then
    echo "   ✅ DNS 解析正常"
else
    echo "   ⚠️ DNS 仍被 VPN 重定向"
fi

echo ""
echo "2. 测试代理连接..."
result=$(curl -s --max-time 10 --proxy "http://spwd19mn8t:<EMAIL>:10001" "https://httpbin.org/ip" 2>&1)

if echo "$result" | grep -q '"origin"'; then
    proxy_ip=$(echo "$result" | grep -o '"origin":"[^"]*"' | cut -d'"' -f4)
    echo "   ✅ 代理连接成功 - IP: $proxy_ip"
else
    echo "   ❌ 代理连接失败"
    echo "   错误: $(echo "$result" | head -1)"
fi

echo ""
echo "3. 测试服务代理状态..."
curl -s "https://getgoodtape-video-proc.fly.dev/proxy-stats" | jq -r '.proxy_list_sample[0]' | head -1

echo ""
echo "4. 建议的完整 Shadowrocket 配置："
echo "   IP-CIDR,***********/20,DIRECT"
echo "   IP-CIDR,*************/24,DIRECT"
echo "   DOMAIN,gate.decodo.com,DIRECT"
echo "   DOMAIN-SUFFIX,decodo.com,DIRECT"