# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/workers/node_modules
/.pnp
.pnp.js

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local
.env

# Cloudflare Workers environment files
workers/.dev.vars
workers/.prod.vars
workers/.wrangler/

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# IDE
.vscode/
.idea/

# OS
Thumbs.db

# Data files
/data/
# Ignore data JSON files but not config files
/data/*.json
*.data.json

# Security - API keys and secrets
**/config/secrets.py
**/config/api_keys.py
**/*_secrets.py
**/*_keys.py

# Python cache and temporary files
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
*.so
.pytest_cache/

# Backup files
*.backup
*.bak
*~
*_backup
*.direct_backup
*.no_proxy_backup

# Test reports and temporary files
*_test_report.json
frontend_test_report.json

# Temporary scripts and debug files
test_after_ip_fix.sh
test_ip_direct.py
test_ip_proxy_direct.py
test_manual.sh
test_proxy_fix.sh
test_dns_override.sh
shadowrocket_debug.sh
create_direct_only_version.py
create_ip_bypass.py
create_no_proxy_version.py

# API client backups
lib/api-client.ts.backup
